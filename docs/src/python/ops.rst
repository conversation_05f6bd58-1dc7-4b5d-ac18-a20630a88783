.. _ops:

Operations
==========

.. currentmodule:: mlx.core

.. autosummary::
  :toctree: _autosummary

   abs
   add
   addmm
   all
   allclose
   any
   arange
   arccos
   arccosh
   arcsin
   arcsinh
   arctan
   arctan2
   arctanh
   argmax
   argmin
   argpartition
   argsort
   array_equal
   as_strided
   atleast_1d
   atleast_2d
   atleast_3d
   bitwise_and
   bitwise_invert
   bitwise_or
   bitwise_xor
   block_masked_mm
   broadcast_arrays
   broadcast_to
   ceil
   clip
   concatenate
   contiguous
   conj
   conjugate
   convolve
   conv1d
   conv2d
   conv3d
   conv_transpose1d
   conv_transpose2d
   conv_transpose3d
   conv_general
   cos
   cosh
   cummax
   cummin
   cumprod
   cumsum
   degrees
   dequantize
   diag
   diagonal
   divide
   divmod
   einsum
   einsum_path
   equal
   erf
   erfinv
   exp
   expm1
   expand_dims
   eye
   flatten
   floor
   floor_divide
   full
   gather_mm
   gather_qmm
   greater
   greater_equal
   hadamard_transform
   identity
   imag
   inner
   isfinite
   isclose
   isinf
   isnan
   isneginf
   isposinf
   issubdtype
   kron
   left_shift
   less
   less_equal
   linspace
   load
   log
   log2
   log10
   log1p
   logaddexp
   logcumsumexp
   logical_not
   logical_and
   logical_or
   logsumexp
   matmul
   max
   maximum
   mean
   meshgrid
   min
   minimum
   moveaxis
   multiply
   nan_to_num
   negative
   not_equal
   ones
   ones_like
   outer
   partition
   pad
   power
   prod
   put_along_axis
   quantize
   quantized_matmul
   radians
   real
   reciprocal
   remainder
   repeat
   reshape
   right_shift
   roll
   round
   rsqrt
   save
   savez
   savez_compressed
   save_gguf
   save_safetensors
   sigmoid
   sign
   sin
   sinh
   slice
   slice_update
   softmax
   sort
   split
   sqrt
   square
   squeeze
   stack
   std
   stop_gradient
   subtract
   sum
   swapaxes
   take
   take_along_axis
   tan
   tanh
   tensordot
   tile
   topk
   trace
   transpose
   tri
   tril
   triu
   unflatten
   var
   view
   where
   zeros
   zeros_like
